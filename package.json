{"name": "lms_user", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-react": "^9.0.0", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@headlessui/react": "^1.7.17", "@hookform/resolvers": "^3.3.1", "@iconify/react": "^4.1.1", "@react-pdf/renderer": "^3.3.8", "@reduxjs/toolkit": "^1.9.5", "@rollup/plugin-replace": "^5.0.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@svg-maps/world": "^1.0.1", "@tanstack/react-query": "^5.17.19", "@tippyjs/react": "^4.2.6", "@vitejs/plugin-react-refresh": "^1.3.6", "apexcharts": "^3.42.0", "axios": "^1.6.5", "bangla-number-converter": "^1.0.1", "chart.js": "^4.4.0", "ckeditor5": "^43.0.0", "cleave.js": "^1.6.0", "d3-array": "^3.2.4", "dayjs": "^1.11.9", "dompurify": "^3.1.6", "firebase": "^10.12.2", "formik": "^2.4.5", "framer-motion": "^12.9.4", "hls.js": "^1.5.18", "i18next": "^23.16.8", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "moment": "^2.30.1", "pusher-js": "^8.4.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-avatar-editor": "^13.0.2", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.17.1", "react-calendar": "^4.6.0", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-collapse": "^5.1.1", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.46.1", "react-i18next": "^14.0.0", "react-icons": "^5.3.0", "react-leaflet": "^4.2.1", "react-pdf": "^7.7.1", "react-player": "^2.15.1", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-select": "^5.7.4", "react-step-progress": "^1.0.3", "react-step-progress-bar": "^1.0.3", "react-table": "^7.8.0", "react-tailwindcss-datepicker": "^1.6.6", "react-toastify": "^9.1.3", "react-transition-group": "^4.4.5", "recharts": "^2.8.0", "sass": "^1.66.1", "simplebar-react": "^3.2.4", "sort-by": "^0.0.2", "sweetalert2": "^11.6.13", "swiper": "^8.4.5", "uuidv4": "^6.2.13", "yup": "^1.2.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}