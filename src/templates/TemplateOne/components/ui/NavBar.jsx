import React from "react";
import { NavLink, useParams } from "react-router-dom";
import { FaChevronDown, FaChevronRight } from "react-icons/fa"; // Icon package for dropdown arrow

const NavBar = ({ menuItems }) => {
  const primaryCategories = menuItems?.slice(0, 4); // First two categories
  const moreCategories = menuItems?.slice(4); // Remaining categories for "More"

  const { menuId, subMenuId } = useParams(); // Get IDs from URL parameters

  return (
    <nav>
      <div className="container mx-auto py-2 flex justify-between items-center">
        <div className="flex gap-6">
          {/* Primary Categories */}
          {primaryCategories?.map((category) => {
            const isActive = menuId == category.id; // Check if the category is active
            return (
              <div className="relative group" key={category.id}>
                <NavLink
                  to={category?.is_custom_page ? `/${category.slug}` : `/courses/${category.id}`}
                  className={`flex items-center gap-1 hover:text-gray-400 ${
                    isActive ? "text-blue-500 font-bold" : ""
                  }`}
                >
                  {category.name}
                  {category.has_submenu &&
                  category.sub_categories?.length > 0 && (
                    <FaChevronDown
                      className={`text-sm transition-transform ${
                        isActive ? "rotate-180" : ""
                      }`}
                    />
                  )}
                </NavLink>
                {category.has_submenu && category.sub_categories?.length > 0 && (
                  <div
                    className="absolute left-0 hidden group-hover:block bg-white text-black rounded shadow-lg w-56"
                  >
                    {category.sub_categories.map((sub) => {
                      const isSubActive =
                        isActive && subMenuId == sub.id; // Check if the submenu is active
                      return (
                        <NavLink
                          key={sub.id}
                          to={`/courses/${category.id}/${sub.id}`}
                          className={`block px-4 py-2 hover:bg-gray-200 ${
                            isSubActive ? "bg-gray-100 text-blue-500 font-bold" : ""
                          }`}
                        >
                          {sub.name}
                        </NavLink>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}

          {/* More Dropdown */}
          {moreCategories?.length > 0 && (
            <div className="relative group">
              <span className="flex items-center gap-1 cursor-pointer hover:text-gray-400">
                More
                <FaChevronDown className="text-sm transition-transform group-hover:rotate-180" />
              </span>
              <div
                className="absolute left-0 hidden group-hover:block bg-white text-black rounded shadow-lg w-56"
              >
                {moreCategories?.map((category) => {
                  const isActive = menuId == category.id;
                  return (
                    <div className="relative group" key={category.id}>
                      <NavLink
                        to={category?.is_custom_page ? `/${category.slug}` : `/courses/${category.id}`}
                        className={`flex items-center gap-1 px-4 py-2 hover:bg-gray-200 ${
                          isActive ? "text-blue-500 font-bold" : ""
                        }`}
                      >
                        {category.name}
                        {category.sub_categories?.length > 0 && (
                          <FaChevronRight
                            className={`text-sm transition-transform`}
                          />
                        )}
                      </NavLink>
                      {category.has_submenu &&
                        category.sub_categories?.length > 0 && (
                          <div
                            className="absolute left-full top-0 mt-0 hidden group-hover:block bg-white text-black rounded shadow-lg w-60"
                          >
                            {category.sub_categories.map((sub) => {
                              const isSubActive =
                                isActive && subMenuId == sub.id;
                              return (
                                <NavLink
                                  key={sub.id}
                                  to={`/courses/${category.id}/${sub.id}`}
                                  className={`block px-4 py-2 hover:bg-gray-200 ${
                                    isSubActive
                                      ? "bg-gray-100 text-blue-500 font-bold"
                                      : ""
                                  }`}
                                >
                                  {sub.name}
                                </NavLink>
                              );
                            })}
                          </div>
                        )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default NavBar;
