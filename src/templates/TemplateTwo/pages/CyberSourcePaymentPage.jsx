import React, { useEffect, useState } from "react";
import api from "@/server/api";
import { useNavigate } from "react-router-dom";

export default function CyberSourcePaymentPage({
  amount = 100,
  currency = "USD",
  onSuccess,
  onError,
}) {
  const [microform, setMicroform] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  // Add debug logging
  useEffect(() => {
    console.log("CyberSourcePaymentPage component mounted");
    console.log("Current location:", window.location.href);
    console.log("Props received:", { amount, currency, onSuccess, onError });

    // Add a visible alert to make sure the component is loading
    setTimeout(() => {
      console.log("Component should be visible now");
    }, 1000);
  }, []);

  // Load Flex SDK and initialize microform
  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://flex.cybersource.com/cybersource/assets/microform/0.4.0/flex-microform.min.js";
    script.async = true;

    script.onload = async () => {
      try {
        console.log("CyberSource script loaded, making API call...");
        const res = await api.get("cybersource/session");
        const sessionData = res.data;
        console.log("API Response:", res);

        if (window.Flex) {
          console.log("Flex available:", window.Flex);
          // For now, just set loading to false to show the form
          setLoading(false);
        } else {
          console.error("Flex not available");
          setError("CyberSource Flex not available");
          setLoading(false);
        }

      } catch (err) {
        console.error("CyberSource error:", err);
        setError("Payment setup failed. Please try again.");
        onError?.(err);
        setLoading(false);
      }
    };

    script.onerror = () => {
      const err = new Error("Failed to load CyberSource SDK");
      console.error("Script loading error:", err);
      setError(err.message);
      setLoading(false);
      onError?.(err);
    };

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const handlePayment = async (e) => {
    e.preventDefault();
    if (!microform || isSubmitting) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const { token } = await microform.createToken();

      const response = await api.post("cybersource/pay", {
        paymentToken: token,
        amount,
        currency,
      });

      onSuccess?.(response.data);
    } catch (err) {
      console.error("Payment failed:", err);
      setError(
        err.response?.data?.message ||
          "Payment failed. Please check your card details."
      );
      onError?.(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col gap-4 items-center justify-center p-6 bg-white border rounded shadow-md max-w-md mx-auto mt-10">
      <h2 className="text-xl font-semibold text-green-600">✅ Secure Payment</h2>
      <p className="text-sm text-gray-600 bg-yellow-100 p-2 rounded">CyberSource Payment Page - Component Loaded Successfully</p>
      <p className="text-xs text-blue-600">Debug: This component is rendering correctly!</p>

      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded w-full text-center">
          {error}
        </div>
      )}

      {loading ? (
        <div>
          <p>Loading payment form...</p>
          <p className="text-sm text-gray-500 mt-2">Debug: Amount = ${amount}, Currency = {currency}</p>
        </div>
      ) : (
        <form onSubmit={handlePayment} className="w-full flex flex-col gap-4">
          <div className="w-full">
            <label className="block text-sm font-medium mb-1">Card Number</label>
            <div id="card-number" className="border p-2 rounded bg-white" />
          </div>
          <div className="w-full">
            <label className="block text-sm font-medium mb-1">Expiration Date</label>
            <div id="card-expiry" className="border p-2 rounded bg-white" />
          </div>
          <div className="w-full">
            <label className="block text-sm font-medium mb-1">CVV</label>
            <div id="card-cvv" className="border p-2 rounded bg-white" />
          </div>

          <button
            type="submit"
            disabled={!microform || isSubmitting}
            className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition disabled:opacity-50"
          >
            {isSubmitting ? "Processing..." : `Pay $${amount}`}
          </button>
        </form>
      )}
    </div>
  );
}