import React from 'react';

import 'ckeditor5/ckeditor5.css';
import '@/assets/scss/editor-data.css';

const EditorData = ({htmlData}) => {


return (
  <div
    className="ck-content editor-data-content overflow-auto"
    style={{
      height: '100%',
      width: '100%',
      // Inline styles as fallback to ensure list styling works
      '--list-style-type': 'disc',
      '--list-style-position': 'outside',
      '--list-padding-left': '40px',
    }}
    dangerouslySetInnerHTML={{
      __html: htmlData, // Sanitize the data before rendering
    }}
/>
);

}
export default EditorData;