/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/react-tailwindcss-datepicker/dist/index.esm.js",
  ],
  mode: "jit",
  darkMode: "class",
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: "16px",
        sm: "16px",
        lg: "16px",
        xl: "0",
        "2xl": "0",
      },
      screens: {
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1280px",
      },
    },
    extend: {
      colors: {
        // 'rose-of-sharon'
        valencia6: {
          50: "#fffbeb",
          100: "#fef3c7",
          200: "#fde58a",
          300: "#fbd24e",
          400: "#fabe25",
          500: "#f49d0c",
          600: "#d87607",
          700: "#bc560a",
          800: "#923f0e",
          900: "#78340f",
          950: "#451a03",
        },

        // jade: {
        jade: {
          50: "#D0D0D0",
          100: "#FEFFFF",
          200: "#b8fadd",
          300: "#81f4c3",
          400: "#43e5a0",
          500: "#1acd81",
          600: "#0fa968",
          700: "#108554",
          800: "#126945",
          900: "#11563a",
          950: "#03301f",
        },
        // rose-gold
        // Previous Color Codes
        // primary: {
        //     50: "#fef2f4",
        //     100: "#fde6e9",
        //     200: "#fbd0d9",
        //     300: "#f7aab9",
        //     400: "#f27a93",
        //     500: "#e63f66",
        //     600: "#d42a5b",
        //     700: "#b21e4b",
        //     800: "#951c45",
        //     900: "#801b40",
        //     950: "#470a1f",
        // },

        //New changes for demo
        primary: {
          50: "#F0FBF9",
          100: "#F9FFFD",
          200: "#B6DED4",
          300: "#DEEEC6",
          400: "#F3F9F8",
          500: "#e63f66",
          600: "#006A4E",
          700: "#006A4E",
          800: "#006A4E",
          900: "#006A4E",
          950: "#470a1f",
        },
        // chill
        valencia4: {
          50: "#f2f9f9",
          100: "#ddeff0",
          200: "#bfe0e2",
          300: "#92cace",
          400: "#5faab1",
          500: "#438e96",
          600: "#3b757f",
          700: "#356169",
          800: "#325158",
          900: "#2d464c",
          950: "#1a2c32",
        },

        valenciav: {
          100: "#fae7e6",
          200: "#f7d4d1",
          300: "#f0b6b1",
          400: "#e58b84",
          500: "#d2544a",
          600: "#cd4236",
          700: "#a33a32",
          800: "#87342d",
          900: "#71302b",
        },
        primary: {
          50: '#e0f3ff',   // very light
          100: '#b3dcff',  // light 
          200: '#80c3ff',  // lighter
          300: '#4daaff',  // light
          400: '#2694ff',  // slightly lighter than primary
          500: '#0072BC',  // primary
          600: '#0066a9',  // slightly darker than primary
          700: '#005288',  // darker
          800: '#003d66',  // darker
          900: '#00294d',  // very dark
        },
        secondary: {
          50: "#F9FAFB",
          100: "#F4F5F7",
          200: "#E5E7EB",
          300: "#D2D6DC",
          400: "#9FA6B2",
          500: "#A0AEC0",
          600: "#475569",
          700: "#334155",
          800: "#1E293B",
          900: "#0F172A",
        },
        danger: {
          50: "#FFF7F7",
          100: "#FEEFEF",
          200: "#FCD6D7",
          300: "#FABBBD",
          400: "#F68B8D",
          500: "#F1595C",
          600: "#D75052",
          700: "#913638",
          800: "#6D292A",
          900: "#461A1B",
        },
        black: {
          50: "#F9FAFB",
          100: "#F4F5F7",
          200: "#E5E7EB",
          300: "#D2D6DC",
          400: "#9FA6B2",
          500: "#111112",
          600: "#475569",
          700: "#334155",
          800: "#1E293B",
          900: "#0F172A",
        },
        warning: {
          50: '#ffece0',
          100: '#ffceb3',
          200: '#ffad80',
          300: '#ff8d4d',
          400: '#ff7326',
          500: '#BC4A00',
          600: '#a94400',
          700: '#863800',
          800: '#662c00',
          900: '#4d2200',
        },
        info: {
          50: "#F3FEFF",
          100: "#E7FEFF",
          200: "#C5FDFF",
          300: "#A3FCFF",
          400: "#5FF9FF",
          500: "#0CE7FA",
          600: "#00B8D4",
          700: "#007A8D",
          800: "#005E67",
          900: "#003F42",
        },
        success: {
          50: "#F3FEF8",
          100: "#E7FDF1",
          200: "#C5FBE3",
          300: "#A3F9D5",
          400: "#5FF5B1",
          500: "#50C793",
          600: "#3F9A7A",
          700: "#2E6D61",
          800: "#1F4B47",
          900: "#0F2A2E",
        },
        gray: {
          50: "#F9FAFB",
          100: "#F4F5F7",
          200: "#E5E7EB",
          300: "#D2D6DC",
          400: "#9FA6B2",
          500: "#68768A",
          600: "#475569",
          700: "#334155",
          800: "#1E293B",
          900: "#0F172A",
        },
        iron: {
          50: "#f7f7f7",
          100: "#ededed",
          200: "#dfdfdf",
          300: "#d0d0d0",
          400: "#adadad",
          500: "#999999",
          600: "#888888",
          700: "#7b7b7b",
          800: "#676767",
          900: "#545454",
          950: "#363636",
        },
        blacksqueeze: {
          50: "#f5fffc",
          100: "#c5fff1",
          200: "#8bffe5",
          300: "#48ffd6",
          400: "#13eec3",
          500: "#00d1ac",
          600: "#00a98d",
          700: "#008672",
          800: "#056a5c",
          900: "#0a574d",
          950: "#003630",
        },
        alabaster: {
          50: "#fafafa",
          100: "#efefef",
          200: "#dcdcdc",
          300: "#bdbdbd",
          400: "#989898",
          500: "#7c7c7c",
          600: "#656565",
          700: "#525252",
          800: "#464646",
          900: "#3d3d3d",
          950: "#292929",
        },
        jaggedice: {
          50: "#f3faf9",
          100: "#d8efed",
          200: "#b3e0dd",
          300: "#81c7c5",
          400: "#57a9aa",
          500: "#3d8e8f",
          600: "#2f6f72",
          700: "#295a5c",
          800: "#24494b",
          900: "#223d3f",
          950: "#0f2124",
        },
        azureradiance: {
          50: "#edfbff",
          100: "#d7f5ff",
          200: "#b9eeff",
          300: "#88e7ff",
          400: "#50d6ff",
          500: "#28bbff",
          600: "#11a1ff",
          700: "#0a84e7",
          800: "#0f6bbe",
          900: "#135b95",
          950: "#11375a",
        },
        // -------useStart-------

        pictonBlue: {
          50: '#effaff',
          100: '#def3ff',
          200: '#b6eaff',
          300: '#75dbff',
          400: '#2ccaff',
          500: '#00aeef',
          600: '#0090d4',
          700: '#0073ab',
          800: '#00608d',
          900: '#065074',
          950: '#04334d',
        },
        lilyWhite: {
          50: '#f0fbff',
          100: '#e8f8fe',
          200: '#bbeefc',
          300: '#7ee2fb',
          400: '#3ad4f6',
          500: '#10bfe7',
          600: '#049cc5',
          700: '#057c9f',
          800: '#086984',
          900: '#0d566d',
          950: '#093748',
        },
        shuttleGray: {
          50: '#f7f8f8',
          100: '#edeef1',
          200: '#d7dbe0',
          300: '#b4bbc5',
          400: '#8b96a5',
          500: '#6d7a8a',
          600: '#5c6777',
          700: '#48505c',
          800: '#3e444e',
          900: '#373c43',
          950: '#24272d',
        },

        //Mentor DashBoard Color
        cello: {
          50: '#f1f7fe',
          100: '#e2effc',
          200: '#bedef9',
          300: '#85c3f4',
          400: '#44a4ec',
          500: '#1c89db',
          600: '#0e6bbb',
          700: '#0d5697',
          800: '#0f4a7d',
          900: '#10375c',
          950: '#0c2745',
        },
        fuscousGray: {
          50: '#f6f6f6',
          100: '#e7e7e7',
          200: '#d1d1d1',
          300: '#b0b0b0',
          400: '#888888',
          500: '#6d6d6d',
          600: '#5d5d5d',
          700: '#4d4d4d',
          800: '#454545',
          900: '#3d3d3d',
          950: '#262626',
        },
        shark: {
          50: '#f6f6f6',
          100: '#e7e7e7',
          200: '#d1d1d1',
          300: '#b0b0b0',
          400: '#888888',
          500: '#6d6d6d',
          600: '#5d5d5d',
          700: '#4f4f4f',
          800: '#454545',
          900: '#3d3d3d',
          950: '#202020',
        },
        goldDrop: {
          50: '#fef8ec',
          100: '#fcecc9',
          200: '#f8d78f',
          300: '#f4bc55',
          400: '#f2a42d',
          500: '#eb831726',
          600: '#d05f0f',
          700: '#ad4110',
          800: '#8c3314',
          900: '#732b14',
          950: '#421406',
        },








        // -------useEnd---------
      },

      fontFamily: {
        inter: ["Noto Sans", "sans-serif"],
        kalpurush: ["Kalpurush", "sans-serif"],
      },
      boxShadow: {
        base: "0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16)",
        base2:
          "0px 2px 4px rgba(40, 41, 61, 0.04), 0px 8px 16px rgba(96, 97, 112, 0.16)",
        base3: "16px 10px 40px rgba(15, 23, 42, 0.22)",
        deep: "-2px 0px 8px rgba(0, 0, 0, 0.16)",
        dropdown: "0px 4px 8px rgba(0, 0, 0, 0.08)",

        testi: "0px 4px 24px rgba(0, 0, 0, 0.06)",
        todo: "rgba(235 233 241, 0.6) 0px 3px 10px 0px",
      },
      keyframes: {
        zoom: {
          "0%, 100%": { transform: "scale(0.5)" },
          "50%": { transform: "scale(1)" },
        },
        tada: {
          "0%": { transform: "scale3d(1, 1, 1)" },
          "10%, 20%": {
            transform: "scale3d(1, 1, 0.95) rotate3d(0, 0, 1, -10deg)",
          },
          "30%, 50%, 70%, 90%": {
            transform: "scale3d(1, 1, 1) rotate3d(0, 0, 1, 10deg)",
          },
          "40%, 60%, 80%": {
            transform: "rotate3d(0, 0, 1, -10deg)",
          },
          "100%": { transform: "scale3d(1, 1, 1)" },
        },
      },
      animation: {
        "spin-slow": "spin 3s linear infinite",
        zoom: "zoom 1s ease-in-out infinite",
        tada: "tada 1.5s ease-in-out infinite",
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        '.no-spinner': {
          '-moz-appearance': 'textfield', /* Firefox */
          '-webkit-appearance': 'none', /* Chrome, Safari, Edge */
        },
        '.no-spinner::-webkit-outer-spin-button': {
          '-webkit-appearance': 'none',
          margin: 0,
        },
        '.no-spinner::-webkit-inner-spin-button': {
          '-webkit-appearance': 'none',
          margin: 0,
        },
      };

      addUtilities(newUtilities, ['responsive', 'hover']); // Ensure you specify variants if needed
    },
  ],
};
